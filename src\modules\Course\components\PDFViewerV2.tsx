import React, {useState, useRef, useEffect, useCallback} from 'react';
import {
  View,
  StyleSheet,
  Text,
  TouchableOpacity,
  Dimensions,
  ActivityIndicator,
  Alert,
  Platform,
  TouchableWithoutFeedback,
  ScrollView,
} from 'react-native';
import Pdf from 'react-native-pdf';
import {Winicon} from 'wini-mobile-components';
import {ColorThemes} from '../../../assets/skin/colors';
import {TypoSkin} from '../../../assets/skin/typography';
import ConfigAPI from '../../../Config/ConfigAPI';

import {BaseDA} from '../../../base/BaseDA';

interface PDFViewerV2Props {
  url: string;
  fileName?: string;
  onError?: (error: any) => void;
  onLoadStart?: () => void;
  onLoadEnd?: () => void;
  showControls?: boolean; // Show zoom, page controls
  enablePaging?: boolean; // Enable page navigation
  fitPolicy?: 0 | 1 | 2; // 0=width, 1=height, 2=both
  spacing?: number; // Space between pages
  password?: string; // PDF password if needed
  scale?: number; // Initial scale
  minScale?: number; // Minimum scale
  maxScale?: number; // Maximum scale
  horizontal?: boolean; // Horizontal scrolling
  showsPageIndicator?: boolean; // Show page indicator
  enableAnnotations?: boolean; // Enable annotations (if supported)
  enableZoom?: boolean; // Enable pinch-to-zoom and double-tap zoom
}

const PDFViewerV2: React.FC<PDFViewerV2Props> = ({
  url,
  onError,
  onLoadStart,
  onLoadEnd,
  showControls = false, // Hide controls by default
  enablePaging = false, // Disable paging to show all pages
  fitPolicy = 2, // Fit both width and height
  spacing = 10,
  password = '',
  scale = 1.0,
  minScale = 0.5,
  maxScale = 3.0,
  horizontal = false,
  showsPageIndicator = true,
  enableAnnotations = false,
  enableZoom = true, // Enable zoom by default
}) => {
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(0);
  const [currentScale, setCurrentScale] = useState(scale);
  const [base64Data, setBase64Data] = useState<string | null>(null);
  const [downloadProgress, setDownloadProgress] = useState(0);

  const [calculatedHeight, setCalculatedHeight] = useState<number>(400); // Dynamic height
  const pdfRef = useRef<Pdf>(null);

  // Get full PDF URL with proper encoding
  const getFullPdfUrl = useCallback(() => {
    let processedUrl = url;

    // Clean up URL - replace backslashes and newlines
    processedUrl = processedUrl
      .replace(/\\/g, '/') // Replace backslashes with forward slashes
      .replace(/\n/g, '') // Remove newlines
      .replace(/\r/g, '') // Remove carriage returns
      .trim(); // Remove leading/trailing whitespace

    console.log('🔧 URL Processing:');
    console.log('  - Original URL:', url);
    console.log('  - Cleaned URL:', processedUrl);

    if (processedUrl.startsWith('http://') || processedUrl.startsWith('https://')) {
      return processedUrl;
    }

    // Use ConfigAPI for relative URLs
    const configUrl = ConfigAPI.getValidLink(processedUrl);
    console.log('  - ConfigAPI URL:', configUrl);
    return configUrl;
  }, [url]);

  const fullPdfUrl = getFullPdfUrl();

  // Download PDF as base64 using pdf-proxy API
  
  // Debug logging
  console.log('PDFViewerV2 Debug Info:');
  console.log('- Original URL:', url);
  console.log('- Full PDF URL:', fullPdfUrl);
  console.log('- Loading state:', loading);
  console.log('- Error state:', error);


  // Safely handle scale changes with error boundaries
  useEffect(() => {
    if (pdfRef.current && base64Data) {
      try {
        // Validate scale before applying
        const safeScale = Math.max(0.1, Math.min(5.0, currentScale));
        if (safeScale !== currentScale) {
          console.warn('⚠️ Scale out of safe bounds, adjusting:', currentScale, '->', safeScale);
          setCurrentScale(safeScale);
        }
        console.log('🔄 PDF scale update to:', safeScale);
      } catch (error) {
        console.error('❌ Error updating PDF scale:', error);
        // Reset to safe scale on error
        setCurrentScale(1.0);
      }
    }
  }, [currentScale, base64Data]);
  const handleLoadComplete = (numberOfPages: number, _filePath: string, size: { height: number; width: number; }) => {
    console.log('PDF load completed:', numberOfPages, 'pages');
    console.log('PDF dimensions:', size.width, 'x', size.height);

    setLoading(false);
    setTotalPages(numberOfPages);
    setDownloadProgress(100);

    // Calculate dynamic height based on number of pages and actual PDF dimensions
    const screenWidth = Dimensions.get('window').width - 32; // Account for padding
    const screenHeight = Dimensions.get('window').height;

    // Use actual PDF page dimensions if available, otherwise estimate
    const pdfPageWidth = size.width || screenWidth;
    const pdfPageHeight = size.height || (screenWidth * 1.414); // Fallback to A4 ratio

    // Calculate scale to fit width
    const scaleToFitWidth = screenWidth / pdfPageWidth;
    const scaledPageHeight = pdfPageHeight * scaleToFitWidth;

    // Calculate total height for all pages with spacing
    const totalEstimatedHeight = (scaledPageHeight * numberOfPages) + (spacing * Math.max(0, numberOfPages - 1));

    // Set reasonable bounds: min 400px, max 80% of screen height for scrollable area
    const maxAllowedHeight = screenHeight * 0.8;
    const dynamicHeight = Math.min(Math.max(totalEstimatedHeight, 400), maxAllowedHeight);

    console.log('📏 PDF Height Calculation:');
    console.log('  - Pages:', numberOfPages);
    console.log('  - PDF page size:', pdfPageWidth, 'x', pdfPageHeight);
    console.log('  - Screen width:', screenWidth);
    console.log('  - Scale to fit:', scaleToFitWidth);
    console.log('  - Scaled page height:', scaledPageHeight);
    console.log('  - Total estimated height:', totalEstimatedHeight);
    console.log('  - Final dynamic height:', dynamicHeight);

    setCalculatedHeight(dynamicHeight);

    if (onLoadEnd) {
      onLoadEnd();
    }
  };

  const downloadPdfAsBase64 = useCallback(async () => {
    if (!fullPdfUrl) {
      return; // No URL to download
    }

    try {
      console.log('📥 Starting PDF download via pdf-proxy API...');
      console.log('  - URL:', fullPdfUrl);

      setLoading(true);
      setError(null);
      setDownloadProgress(0);

      if (onLoadStart) {
        onLoadStart();
      }
      // Call pdf-proxy API
      const urlAPI = ConfigAPI.url + 'data/pdf-proxy?url=' + encodeURIComponent('https://www.adobe.com/support/products/enterprise/knowledgecenter/media/c4611_sample_explain.pdf');
      const response = await fetch(urlAPI, {
        method: 'GET',
        headers: {
          'accept': 'application/json',
        },
      });
      const responseJson = await response.json();
      if (responseJson?.data) {
        const base64String = responseJson?.data;

        // Validate base64 data
        if (!base64String || base64String.length === 0) {
          throw new Error('Empty base64 data received from pdf-proxy API');
        }

        const base64Uri = `data:application/pdf;base64,${base64String}`;

        console.log('✅ PDF downloaded successfully via pdf-proxy');
        console.log('  - Base64 length:', base64String.length);
        console.log('  - File size:', Math.round(base64String.length * 0.75 / 1024), 'KB');

        setBase64Data(base64Uri);
        setDownloadProgress(100);
        setLoading(false);

        if (onLoadEnd) {
          onLoadEnd();
        }
      } else {
        throw new Error(`PDF-proxy API failed: ${responseJson?.message || 'Unknown error'}`);
      }
    } catch (downloadError: any) {
      console.error('❌ PDF download via pdf-proxy failed:', downloadError);
      console.error('  - Error message:', downloadError?.message);

      setLoading(false);
      setError(`Không thể tải PDF: ${downloadError?.message || 'Unknown error'}`);

      if (onError) {
        onError(downloadError);
      }
    }
  }, [fullPdfUrl, onLoadStart, onLoadEnd, onError]);

  // Initialize component - moved after downloadPdfAsBase64 definition
  useEffect(() => {
    console.log('PDFViewerV2 initialized for URL:', url);

    setLoading(true);
    setError(null);
    setCurrentPage(1);
    setTotalPages(0);
    setCurrentScale(scale);
    setBase64Data(null);
    setDownloadProgress(0);

    // Always use pdf-proxy API to download base64
    downloadPdfAsBase64();
  }, [url, downloadPdfAsBase64]); // ✅ Thêm url và downloadPdfAsBase64 vào dependency array để theo dõi thay đổi

  const handleLoadProgress = (percent: number) => {
    console.log('PDF load progress:', percent);
    // Progress is handled by downloadPdfAsBase64 function
  };

  const handleError = (pdfError: any) => {
    console.error('PDF load error:', pdfError);
    console.error('PDF URL that failed:', fullPdfUrl);

    setLoading(false);

    // Reset scale to safe value on error to prevent crashes
    setCurrentScale(1.0);

    let errorMessage = 'Không thể tải tài liệu PDF.';
    if (pdfError && pdfError.message) {
      if (pdfError.message.includes('network')) {
        errorMessage = 'Lỗi kết nối mạng. Vui lòng kiểm tra internet.';
      } else if (pdfError.message.includes('format')) {
        errorMessage = 'File PDF không hợp lệ hoặc bị hỏng.';
      } else if (pdfError.message.includes('permission')) {
        errorMessage = 'Không có quyền truy cập file PDF.';
      } else if (pdfError.message.includes('scale') || pdfError.message.includes('zoom')) {
        errorMessage = 'Lỗi zoom PDF. Đang reset về mặc định.';
        // Don't call onError for zoom-related errors, just reset
        return;
      }
    }

    setError(errorMessage + ' Vui lòng thử lại.');
    if (onError) {
      onError(pdfError);
    }
  };

  const handlePageChanged = (page: number, numberOfPages: number) => {
    console.log('Page changed:', page, 'of', numberOfPages);
    setCurrentPage(page);
    setTotalPages(numberOfPages);
  };

  const handleScaleChanged = useCallback((newScale: number) => {
    console.log('📏 Scale changed:', newScale);

    // Validate scale value to prevent crashes
    if (!newScale || isNaN(newScale) || !isFinite(newScale)) {
      console.warn('⚠️ Invalid scale value:', newScale, 'Resetting to 1.0');
      setCurrentScale(1.0);
      return;
    }

    // Ensure scale is within safe bounds
    const safeMinScale = Math.max(0.1, minScale); // Never go below 0.1
    const safeMaxScale = Math.min(5.0, maxScale); // Never go above 5.0
    const boundedScale = Math.max(safeMinScale, Math.min(safeMaxScale, newScale));

    console.log('  - Original scale:', newScale);
    console.log('  - Bounded scale:', boundedScale);
    console.log('  - Current scale:', currentScale);
    console.log('  - Safe bounds:', safeMinScale, 'to', safeMaxScale);

    // Only update if scale actually changed to prevent loops
    if (Math.abs(currentScale - boundedScale) > 0.01) {
      setCurrentScale(boundedScale);
    }
  }, [minScale, maxScale, currentScale]);

  // Control functions
  const goToPage = (pageNumber: number) => {
    if (pageNumber >= 1 && pageNumber <= totalPages && pdfRef.current) {
      pdfRef.current.setPage(pageNumber);
    }
  };

  const goToPreviousPage = () => {
    if (currentPage > 1) {
      goToPage(currentPage - 1);
    }
  };

  const goToNextPage = () => {
    if (currentPage < totalPages) {
      goToPage(currentPage + 1);
    }
  };





  const renderError = () => (
    <View style={styles.errorContainer}>
      <Winicon
        src="outline/user interface/alert-triangle"
        size={48}
        color={ColorThemes.light.Error_Color_Main}
      />
      <Text style={styles.errorText}>{error}</Text>
      <TouchableOpacity
        style={styles.retryButton}
        onPress={() => {
          setError(null);
          setLoading(true);
          setBase64Data(null);
          setDownloadProgress(0);
          downloadPdfAsBase64();
        }}>
        <Text style={styles.retryButtonText}>Thử lại</Text>
      </TouchableOpacity>
    </View>
  );

  const renderLoading = () => (
    <View style={styles.loadingContainer}>
      <ActivityIndicator
        size="large"
        color={ColorThemes.light.Primary_Color_Main}
      />
      <Text style={styles.loadingText}>
        {`Đang tải xuống PDF... ${downloadProgress > 0 ? `${Math.round(downloadProgress)}%` : ''}`}
      </Text>
      {downloadProgress > 0 && (
        <View style={styles.progressBarContainer}>
          <View style={[styles.progressBar, {width: `${downloadProgress}%`}]} />
        </View>
      )}
    </View>
  );

  return (
    <View style={[styles.container, { height: calculatedHeight }]}>
      {loading && !base64Data && renderLoading()}
      {error && renderError()}
      {!loading && !error && (
        <>
          {/* Wrap PDF in error boundary for zoom crashes */}
          <View style={styles.pdfWrapper}>
            <Pdf
            ref={pdfRef}
            key={`pdf-${url}-${base64Data ? 'base64' : 'url'}`} // Stable key to prevent unnecessary re-renders

            source={
              base64Data
                ? {
                    uri: base64Data,
                    cache: false,
                    ...(Platform.OS === 'ios' && {
                      method: 'GET',
                      headers: {
                        'Accept': 'application/pdf',
                        'Content-Type': 'application/pdf',
                      },
                    }),
                  } // Use base64 data
                : {
                    uri: fullPdfUrl,
                    cache: false,
                    headers: Platform.OS === 'ios'
                      ? {
                          'User-Agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 16_0 like Mac OS X) AppleWebKit/605.1.15',
                          'Accept': 'application/pdf,*/*',
                          'Accept-Language': 'en-US,en;q=0.9',
                        }
                      : {
                          'User-Agent': 'Mozilla/5.0 (compatible; ReactNative)',
                        },
                  }
            }
            password={password}
            scale={Math.max(0.1, Math.min(5.0, currentScale))} // Safe scale bounds
            minScale={Math.max(0.1, minScale)} // Safe minimum
            maxScale={Math.min(5.0, maxScale)} // Safe maximum
            spacing={spacing}
            fitPolicy={0} // Fit width only - CRITICAL for continuous scrolling
            enablePaging={false} // CRITICAL: Disable paging for continuous scroll
            horizontal={false} // Vertical scrolling only
            enableRTL={false}
            enableDoubleTapZoom={false} // Disable to prevent crashes during zoom gestures
            enableAntialiasing={true}
            showsHorizontalScrollIndicator={false}
            showsVerticalScrollIndicator={true} // Show vertical scroll indicator
            style={styles.pdf}
            onLoadComplete={handleLoadComplete}
            onLoadProgress={handleLoadProgress}
            onError={handleError}
            onPageChanged={handlePageChanged}
            onScaleChanged={handleScaleChanged}
            trustAllCerts={Platform.OS === 'ios'} // Trust all certs on iOS for better compatibility
            renderActivityIndicator={() => <></>} // We handle loading ourselves
            singlePage={false} // CRITICAL: Show all pages continuously

            scrollEnabled={true} // CRITICAL: Enable scrolling within PDF
            // Platform-specific optimizations for continuous scrolling
            {...(Platform.OS === 'ios' && {
              enableAnnotationRendering: false, // Disable for better performance
              enablePaging: false, // CRITICAL for iOS
              singlePage: false, // CRITICAL: Show all pages on iOS
            })}
            {...(Platform.OS === 'android' && {
              enablePaging: false, // CRITICAL for Android
              singlePage: false, // CRITICAL: Show all pages on Android
            })}
          />

          {/* Page Navigation Controls - only show if showControls is true */}
          {showControls && totalPages > 1 && (
            <View style={styles.controlsContainer}>
              <View style={styles.pageControls}>
                <TouchableOpacity
                  style={[styles.controlButton, currentPage <= 1 && styles.controlButtonDisabled]}
                  onPress={goToPreviousPage}
                  disabled={currentPage <= 1}>
                  <Winicon
                    src="outline/arrows/chevron-left"
                    size={16}
                    color={currentPage <= 1 ? ColorThemes.light.Neutral_Text_Color_Subtitle : ColorThemes.light.Primary_Color_Main}
                  />
                </TouchableOpacity>

                <Text style={styles.pageIndicator}>
                  {currentPage} / {totalPages}
                </Text>

                <TouchableOpacity
                  style={[styles.controlButton, currentPage >= totalPages && styles.controlButtonDisabled]}
                  onPress={goToNextPage}
                  disabled={currentPage >= totalPages}>
                  <Winicon
                    src="outline/arrows/chevron-right"
                    size={16}
                    color={currentPage >= totalPages ? ColorThemes.light.Neutral_Text_Color_Subtitle : ColorThemes.light.Primary_Color_Main}
                  />
                </TouchableOpacity>
              </View>
            </View>
          )}
        </>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: ColorThemes.light.Neutral_Background_Color_Absolute,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: ColorThemes.light.Neutral_Border_Color_Main,
    overflow: 'hidden',
    // Height will be set dynamically based on PDF content
  },
  pdfWrapper: {
    flex: 1,
    width: '100%',
    height: '100%',
  },
  pdf: {
    backgroundColor: '#fff',
    width: '100%',
    // Height will be set dynamically in the component
    flex: 1,
  },
  loadingContainer: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.9)',
    zIndex: 1,
  },
  loadingText: {
    ...TypoSkin.body2,
    color: ColorThemes.light.Neutral_Text_Color_Subtitle,
    marginTop: 8,
  },
  loadingSubtext: {
    ...TypoSkin.body2,
    fontSize: 12,
    color: ColorThemes.light.Neutral_Text_Color_Subtitle,
    marginTop: 8,
    textAlign: 'center',
  },
  progressBarContainer: {
    width: '80%',
    height: 4,
    backgroundColor: ColorThemes.light.Neutral_Border_Color_Main,
    borderRadius: 2,
    marginTop: 12,
    overflow: 'hidden',
  },
  progressBar: {
    height: '100%',
    backgroundColor: ColorThemes.light.Primary_Color_Main,
    borderRadius: 2,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  errorText: {
    ...TypoSkin.body1,
    color: ColorThemes.light.Error_Color_Main,
    textAlign: 'center',
    marginVertical: 16,
  },
  retryButton: {
    backgroundColor: ColorThemes.light.Primary_Color_Main,
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 6,
  },
  retryButtonText: {
    ...TypoSkin.body2,
    color: ColorThemes.light.Neutral_Background_Color_Absolute,
    fontWeight: '600',
  },
  warningContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
    backgroundColor: ColorThemes.light.Neutral_Background_Color_Absolute,
  },
  warningTitle: {
    ...TypoSkin.title3,
    color: ColorThemes.light.Warning_Color_Main,
    textAlign: 'center',
    marginVertical: 12,
  },
  warningText: {
    ...TypoSkin.body1,
    color: ColorThemes.light.Neutral_Text_Color_Subtitle,
    textAlign: 'center',
    marginBottom: 20,
    lineHeight: 22,
  },
  warningButtons: {
    flexDirection: 'row',
    gap: 12,
  },
  warningButtonPrimary: {
    backgroundColor: ColorThemes.light.Primary_Color_Main,
    paddingHorizontal: 20,
    paddingVertical: 10,
    borderRadius: 6,
  },
  warningButtonSecondary: {
    backgroundColor: 'transparent',
    paddingHorizontal: 20,
    paddingVertical: 10,
    borderRadius: 6,
    borderWidth: 1,
    borderColor: ColorThemes.light.Neutral_Border_Color_Main,
  },
  warningButtonPrimaryText: {
    ...TypoSkin.body2,
    color: ColorThemes.light.Neutral_Background_Color_Absolute,
    fontWeight: '600',
  },
  warningButtonSecondaryText: {
    ...TypoSkin.body2,
    color: ColorThemes.light.Neutral_Text_Color_Title,
    fontWeight: '600',
  },
  controlsContainer: {
    position: 'absolute',
    bottom: 10,
    left: 10,
    right: 10,
    borderRadius: 8,
    padding: 8,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  pageControls: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  zoomControls: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  controlButton: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: ColorThemes.light.Neutral_Background_Color_Main,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: ColorThemes.light.Neutral_Border_Color_Main,
  },
  controlButtonDisabled: {
    opacity: 0.5,
  },
  pageIndicator: {
    ...TypoSkin.body2,
    color: ColorThemes.light.Neutral_Text_Color_Title,
    fontWeight: '600',
    minWidth: 60,
    textAlign: 'center',
  },
  zoomText: {
    ...TypoSkin.body2,
    color: ColorThemes.light.Primary_Color_Main,
    fontWeight: '600',
    fontSize: 10,
  },
});

export default PDFViewerV2;